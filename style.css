/* Global Styles */
:root {
    --primary-color: #2ecc71;
    --secondary-color: #27ae60;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --text-color: #34495e;
    --transition: all 0.3s ease;
    --glass-bg: rgba(255, 255, 255, 0.9);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-border: 1px solid rgba(255, 255, 255, 0.18);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

body {
    font-family: 'Tajawal', sans-serif;
    color: var(--text-color);
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* Navbar Styles - Modern 2025 Design */
.navbar-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--glass-shadow);
    border-bottom: var(--glass-border);
    padding: 0.8rem 0;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.navbar-modern.scrolled {
    padding: 0.5rem 0;
    background: rgba(255, 255, 255, 0.98);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.logo-icon {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.navbar-brand:hover .logo-icon {
    transform: rotate(15deg);
}

.brand-content {
    display: flex;
    flex-direction: column;
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1.2;
}

.brand-tagline {
    font-size: 0.75rem;
    color: var(--primary-color);
    font-weight: 500;
}

.nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.6rem 1rem;
    margin: 0 0.3rem;
    border-radius: 8px;
    position: relative;
    transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.nav-text {
    position: relative;
}

.nav-text::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover .nav-text::after,
.nav-link.active .nav-text::after {
    width: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(46, 204, 113, 0.05);
}

.navbar-actions {
    margin-right: 1rem;
}

.btn-contact {
    background: var(--gradient-primary);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    transition: all 0.3s ease;
    border: none;
}

.btn-contact:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
    color: white;
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    padding-top: 80px;
    overflow: hidden;
    background: linear-gradient(135deg, #f0f6ff 0%, #f5fcf9 100%);
}

.hero-background-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(rgba(46, 204, 113, 0.3) 3px, transparent 3px), 
        radial-gradient(rgba(46, 204, 113, 0.2) 2px, transparent 2px);
    background-size: 30px 30px, 15px 15px;
    background-position: 0 0, 15px 15px;
    animation: particleShift 60s linear infinite;
    opacity: 1;
    z-index: 0;
}

.hero-shape-1 {
    position: absolute;
    top: 15%;
    right: 5%;
    width: 300px;
    height: 300px;
    border-radius: 38% 62% 70% 30% / 30% 30% 70% 70%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.2) 0%, rgba(39, 174, 96, 0.08) 100%);
    animation: morphing 15s linear infinite alternate;
    opacity: 0.6;
}

.hero-shape-2 {
    position: absolute;
    bottom: 15%;
    left: 10%;
    width: 250px;
    height: 250px;
    border-radius: 58% 42% 38% 62% / 42% 55% 45% 58%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(46, 204, 113, 0.05) 100%);
    animation: morphing 12s linear infinite alternate-reverse;
    opacity: 0.5;
}

.hero-shape-3 {
    position: absolute;
    top: 40%;
    left: 30%;
    width: 150px;
    height: 150px;
    border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.08) 0%, rgba(46, 204, 113, 0.04) 100%);
    animation: morphing 10s linear infinite;
    opacity: 0.4;
}

@keyframes morphing {
    0% {
        border-radius: 38% 62% 70% 30% / 30% 30% 70% 70%;
    }
    25% {
        border-radius: 58% 42% 38% 62% / 42% 55% 45% 58%;
    }
    50% {
        border-radius: 45% 55% 52% 48% / 60% 40% 60% 40%;
    }
    75% {
        border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
    }
    100% {
        border-radius: 38% 62% 64% 36% / 48% 33% 67% 52%;
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 2rem 0;
}

.hero-badge {
    display: inline-block;
    background: rgba(46, 204, 113, 0.1);
    color: var(--primary-color);
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(46, 204, 113, 0.2);
    backdrop-filter: blur(5px);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.1);
    transform: translateY(0);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hero-badge:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.2);
}

.hero-title {
    font-size: 3.8rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
    line-height: 1.2;
    background: linear-gradient(to right, var(--dark-color), #3a6073);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGradient 8s ease infinite;
}

@keyframes textGradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.highlight {
    position: relative;
    color: var(--primary-color);
    background: linear-gradient(to right, var(--primary-color), #3498db);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% auto;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background: rgba(46, 204, 113, 0.2);
    z-index: -1;
    border-radius: 10px;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-color);
    margin-bottom: 2rem;
    max-width: 90%;
    line-height: 1.8;
}

.hero-cta {
    display: flex;
    gap: 1.2rem;
    margin-bottom: 2.5rem;
}

.btn-hero-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.9rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    z-index: 1;
}

.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-hero-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(46, 204, 113, 0.4);
    color: white;
}

.btn-hero-primary:hover::before {
    width: 100%;
}

.btn-hero-primary i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.btn-hero-primary:hover i {
    transform: translateX(-5px);
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--dark-color);
    border: 1px solid rgba(44, 62, 80, 0.1);
    padding: 0.9rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn-hero-secondary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    color: var(--primary-color);
}

.btn-hero-secondary i {
    font-size: 1.1rem;
    color: var(--primary-color);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.stat-item {
    position: relative;
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem 1rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(46, 204, 113, 0.1);
}

.stat-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.08);
}

.stat-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 0.8rem;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.3);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
}

.counter {
    display: inline-block;
}

.hero-image-col {
    position: relative;
    z-index: 2;
}

.hero-image-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    transform: none;
    transition: box-shadow 0.3s ease;
}

.hero-image-wrapper:hover {
    transform: none;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.hero-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transform: none;
    transition: none;
}

.hero-image-wrapper:hover .hero-image {
    transform: none;
}

.hero-image-shape {
    position: absolute;
    top: -10%;
    right: -10%;
    width: 200px;
    height: 200px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
}

.hero-badge-float {
    position: absolute;
    bottom: 30px;
    left: 30px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 0.8rem 1.2rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    font-weight: 600;
    color: var(--dark-color);
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: translateY(0);
}

.hero-badge-float:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.badge-icon {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.3);
}

.float-icon {
    width: 20px;
    height: 20px;
}

.hero-image-overlay {
    display: none;
}

.hero-wave {
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    filter: drop-shadow(0px -5px 5px rgba(0, 0, 0, 0.03));
}

/* Section Styles */
.section-padding {
    padding: 100px 0;
}

.section-header {
    margin-bottom: 3rem;
}

.text-accent {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.section-title {
    color: var(--dark-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.divider {
    width: 80px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 50px;
    margin-bottom: 1.5rem;
}

/* About Section - Modern Design */
.about-wrapper {
    position: relative;
    z-index: 1;
}

.about-img-container {
    position: relative;
    margin-right: 1.5rem;
}

.about-img-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transform: perspective(1000px) rotateY(3deg) rotateX(3deg);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.about-img-wrapper:hover {
    transform: perspective(1000px) rotateY(0) rotateX(0);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.about-img-wrapper img {
    width: 100%;
    height: auto;
    transform: scale(1);
    transition: transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.about-img-wrapper:hover img {
    transform: scale(1.05);
}

.about-img-badge {
    position: absolute;
    bottom: 30px;
    right: -20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    backdrop-filter: blur(10px);
    padding: 0.9rem 1.3rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    color: var(--dark-color);
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 3;
    transform: translateY(0);
}

.about-img-badge:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.about-img-badge .badge-icon {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.3);
}

.about-img-shape-1 {
    position: absolute;
    top: -15%;
    left: -12%;
    width: 180px;
    height: 180px;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15) 0%, rgba(46, 204, 113, 0.05) 100%);
    z-index: -1;
    animation: floatingShape 8s ease-in-out infinite alternate;
}

.about-img-shape-2 {
    position: absolute;
    bottom: -10%;
    right: -8%;
    width: 120px;
    height: 120px;
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.08) 0%, rgba(39, 174, 96, 0.03) 100%);
    z-index: -1;
    animation: floatingShape 6s ease-in-out infinite alternate-reverse;
}

.about-img-dots {
    position: absolute;
    bottom: 30px;
    left: -15px;
    width: 80px;
    height: 80px;
    background-image: radial-gradient(rgba(46, 204, 113, 0.4) 2px, transparent 2px);
    background-size: 10px 10px;
    z-index: -1;
}

@keyframes floatingShape {
    0% {
        transform: translateY(0) rotate(0);
    }
    100% {
        transform: translateY(15px) rotate(5deg);
    }
}

.about-content {
    position: relative;
    padding: 2rem;
}

.about-text-wrapper {
    position: relative;
    margin-bottom: 2rem;
}

.about-intro {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color);
    padding-right: 1rem;
    border-right: 3px solid var(--primary-color);
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-item {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 1.2rem;
    padding: 1.5rem;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    z-index: 1;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 0;
    background: var(--primary-color);
    transition: height 0.4s ease;
    z-index: 0;
}

.feature-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
}

.feature-item:hover::before {
    height: 100%;
}

.feature-icon {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.2) 0%, rgba(46, 204, 113, 0.1) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
    transition: all 0.4s ease;
    z-index: 1;
}

.feature-item:hover .feature-icon {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 10px 20px rgba(46, 204, 113, 0.2);
}

.feature-text {
    position: relative;
    z-index: 1;
}

.feature-text h4 {
    margin-top: 0;
    margin-bottom: 0.8rem;
    color: var(--dark-color);
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-text h4 {
    color: var(--primary-color);
    transform: translateX(8px);
}

.feature-text p {
    margin: 0;
    color: var(--text-color);
    line-height: 1.6;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-text p {
    transform: translateX(8px);
}

.about-cta {
    display: flex;
    justify-content: flex-start;
    margin-top: 1rem;
}

.btn-about-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 0.8rem 1.8rem;
    border-radius: 50px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow: 0 8px 20px rgba(46, 204, 113, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-about-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-about-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(46, 204, 113, 0.3);
    color: white;
}

.btn-about-primary:hover::before {
    width: 100%;
}

.btn-about-primary i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.btn-about-primary:hover i {
    transform: translateX(-5px);
}

@media (max-width: 991.98px) {
    .about-img-container {
        margin-right: 0;
        margin-bottom: 2rem;
    }
    
    .about-content {
        padding: 1.5rem 0;
    }
    
    .about-img-badge {
        right: 20px;
    }
}

@media (max-width: 767.98px) {
    .features-grid {
        gap: 1rem;
    }
    
    .feature-item {
        padding: 1.2rem;
    }
    
    .about-img-shape-1,
    .about-img-shape-2 {
        display: none;
    }
    
    .about-cta {
        justify-content: center;
    }
}

/* Products Section - Modern Design */
.products-section {
    position: relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #f0f0f0 100%);
    overflow: hidden;
}

.products-header {
    margin-bottom: 4rem;
}

.products-container {
    position: relative;
    padding: 2rem 0;
}

.products-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

.products-circle-1 {
    position: absolute;
    top: 10%;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0.02) 100%);
    z-index: -1;
}

.products-circle-2 {
    position: absolute;
    bottom: 15%;
    left: -80px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.03) 0%, rgba(52, 152, 219, 0.02) 100%);
    z-index: -1;
}

.products-dots {
    position: absolute;
    top: 40%;
    left: 10%;
    width: 180px;
    height: 180px;
    background-image: radial-gradient(rgba(46, 204, 113, 0.3) 2px, transparent 2px);
    background-size: 20px 20px;
    z-index: -1;
    opacity: 0.5;
}

.product-item {
    position: relative;
    margin-bottom: 5rem;
    z-index: 1;
}

.product-number {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10rem;
    font-weight: 800;
    color: rgba(46, 204, 113, 0.07);
    line-height: 1;
    pointer-events: none;
    z-index: -1;
}

.product-content {
    position: relative;
    display: flex;
    align-items: center;
    gap: 3rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.05);
    padding: 3rem;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
}

.product-item:hover .product-content {
    transform: translateY(-10px);
    box-shadow: 0 30px 70px rgba(0, 0, 0, 0.08);
}

.product-image-wrapper {
    position: relative;
    flex: 0 0 300px;
}

.product-image {
    position: relative;
    width: 100%;
    height: 300px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.product-item:hover .product-image img {
    transform: scale(1.08);
}

.product-badge {
    position: absolute;
    bottom: -20px;
    left: 30px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    font-size: 1.5rem;
    border-radius: 50%;
    box-shadow: 0 10px 20px rgba(46, 204, 113, 0.3);
    z-index: 2;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.product-item:hover .product-badge {
    transform: translateY(-10px) scale(1.1);
}

.product-info {
    flex: 1;
}

.product-tag {
    display: inline-block;
    background: rgba(46, 204, 113, 0.1);
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.4rem 1rem;
    border-radius: 50px;
    margin-bottom: 1rem;
    border: 1px solid rgba(46, 204, 113, 0.2);
    transition: all 0.3s ease;
}

.product-item:hover .product-tag {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.product-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.product-item:hover .product-title {
    color: var(--primary-color);
}

.product-description {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.product-features-list {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

.product-features-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    transition: transform 0.3s ease;
}

.product-features-list li i {
    color: var(--primary-color);
}

.product-item:hover .product-features-list li {
    transform: translateX(-5px);
}

.btn-product-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--dark-color);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    padding-bottom: 3px;
    transition: all 0.3s ease;
}

.btn-product-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0.3);
    transform-origin: right;
    transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.btn-product-link:hover {
    color: var(--primary-color);
}

.btn-product-link:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.btn-product-link i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.btn-product-link:hover i {
    transform: translateX(-5px);
}

.products-action {
    margin-top: 2rem;
}

.btn-products-action {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 15px 30px rgba(46, 204, 113, 0.25);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-products-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-products-action:hover {
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(46, 204, 113, 0.35);
}

.btn-products-action:hover::before {
    width: 100%;
}

.btn-products-action i {
    transition: transform 0.3s ease;
}

.btn-products-action:hover i {
    transform: translateX(-5px);
}

@media (max-width: 991.98px) {
    .product-content {
        flex-direction: column;
        padding: 2rem;
        gap: 2rem;
    }
    
    .product-image-wrapper {
        flex: 0 0 100%;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }
    
    .product-image {
        height: 250px;
    }
    
    .product-features-list {
        grid-template-columns: 1fr;
    }
    
    .product-number {
        display: none;
    }
}

@media (max-width: 767.98px) {
    .products-header {
        margin-bottom: 2rem;
    }
    
    .product-item {
        margin-bottom: 3rem;
    }
    
    .product-content {
        padding: 1.5rem;
    }
    
    .product-badge {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        bottom: -15px;
        left: 20px;
    }
    
    .product-title {
        font-size: 1.5rem;
    }
}

/* Reset products-wrapper and old classes to remove old CSS */
.products-wrapper,
.products-bg-shape,
.product-card-modern,
.product-card-inner,
.product-img,
.product-overlay,
.eco-label,
.product-body,
.product-category,
.product-features,
.product-action,
.btn-product-details,
.products-cta,
.btn-products-cta {
    all: initial;
}

/* Benefits Section */
.benefit-card {
    background: white;
    border-radius: 15px;
    padding: 2rem 1.5rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    height: 100%;
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
}

.benefit-card h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.benefit-card p {
    color: var(--text-color);
    margin: 0;
}

/* Vision Section */
.vision-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.vision-content {
    padding: 1rem;
}

.vision-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
}

.vision-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.vision-card h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.vision-card i {
    color: var(--primary-color);
}

.vision-card p {
    margin: 0;
    color: var(--text-color);
}

.values-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.value-tag {
    background: rgba(46, 204, 113, 0.1);
    color: var(--primary-color);
    padding: 0.3rem 1rem;
    border-radius: 30px;
    font-size: 0.9rem;
}

.vision-image {
    position: relative;
}

.vision-badge {
    position: absolute;
    bottom: 30px;
    right: 30px;
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
}

.vision-badge span {
    font-size: 0.9rem;
    display: block;
    margin-bottom: 0.3rem;
}

.vision-badge h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

/* Contact Section */
.contact-info-wrapper {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.contact-text h5 {
    margin: 0 0 0.5rem;
    color: var(--dark-color);
}

.contact-text p {
    margin: 0;
    color: var(--text-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-5px);
}

.contact-form-wrapper {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 204, 113, 0.25);
}

/* Footer */
.footer-modern {
    position: relative;
    background: var(--dark-color);
    color: white;
    margin-top: 4rem;
}

.footer-top {
    padding: 80px 0 50px;
    position: relative;
    z-index: 1;
}

.footer-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(0deg, var(--dark-color) 0%, rgba(46, 61, 80, 0.9) 100%);
    z-index: -1;
}

.footer-brand .logo-icon {
    background: white;
    color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.footer-brand .brand-text {
    color: white;
}

.footer-brand .brand-tagline {
    color: #bdc3c7;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-icon {
    width: 38px;
    height: 38px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.social-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-5px);
}

.footer-widget {
    margin-bottom: 1.5rem;
}

.widget-title {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.8rem;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 2px;
    background: var(--primary-color);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.footer-links a i {
    transition: transform 0.3s ease;
    margin-left: 0.5rem;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(-5px);
}

.footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-contact li {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1rem;
    flex-shrink: 0;
}

.contact-text span {
    display: block;
    font-size: 0.8rem;
    color: #bdc3c7;
    margin-bottom: 0.2rem;
}

.contact-text p {
    margin: 0;
    color: white;
}

.footer-bottom {
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    background: rgba(0, 0, 0, 0.2);
}

.copyright {
    margin: 0;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    justify-content: flex-end;
    gap: 1.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-bottom-links a {
    color: #bdc3c7;
    font-size: 0.9rem;
    text-decoration: none;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 99;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-5px);
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fadeInUp {
    animation: fadeInUp 0.5s ease forwards;
}

/* Media Queries */
@media (max-width: 1199.98px) {
    .hero-title {
        font-size: 3rem;
    }
}

@media (max-width: 991.98px) {
    .section-padding {
        padding: 70px 0;
    }
    
    .hero-content {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .hero-subtitle {
        max-width: 100%;
    }
    
    .hero-cta {
        justify-content: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .navbar-collapse {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: var(--card-shadow);
        margin-top: 1rem;
    }
    
    .navbar-actions {
        margin-top: 1rem;
        margin-right: 0;
    }
    
    .btn-contact {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .copyright {
        text-align: center;
    }
}

@media (max-width: 767.98px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .footer-bottom-links {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .copyright {
        text-align: center;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-hero-primary, .btn-hero-secondary {
        width: 100%;
    }
    
    .contact-form-wrapper, .contact-info-wrapper {
        padding: 1.5rem;
    }
    
    .nav-link {
        padding: 0.8rem 1rem;
        margin: 0.2rem 0;
    }
    
    .footer-widget {
        text-align: center;
    }
    
    .widget-title::after {
        right: 50%;
        transform: translateX(50%);
    }
    
    .footer-links a {
        justify-content: center;
    }
    
    .footer-contact li {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .contact-icon {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 575.98px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-padding {
        padding: 50px 0;
    }
    
    .newsletter-form {
        margin-top: 1rem;
    }
    
    .vision-badge {
        right: 15px;
        bottom: 15px;
        padding: 0.7rem;
    }
    
    .vision-badge h3 {
        font-size: 1.5rem;
    }
}

/* Custom Animation for Navbar on Scroll */
@keyframes navbarScroll {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.navbar-modern.scrolled {
    animation: navbarScroll 0.5s forwards;
}

/* تعديل أزرار Bootstrap الافتراضية */
.btn-primary {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
    transition: all 0.3s ease;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.3) !important;
    transform: translateY(-3px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.2) !important;
    transform: translateY(-3px);
}

@keyframes particleShift {
    0% {
        background-position: 0 0, 0 0;
    }
    100% {
        background-position: 100px 100px, 50px 50px;
    }
}
