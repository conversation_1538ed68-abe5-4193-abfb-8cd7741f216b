/* Global Styles */
:root {
    --primary-color: #2ecc71;
    --secondary-color: #27ae60;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --text-color: #333;
    --transition: all 0.3s ease;
    --glass-bg: rgba(255, 255, 255, 0.9);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-border: 1px solid rgba(255, 255, 255, 0.18);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

body {
    font-family: 'Tajawal', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

/* Navbar Styles - Modern 2025 Design */
.navbar-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--glass-shadow);
    border-bottom: var(--glass-border);
    padding: 0.8rem 0;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.navbar-modern.scrolled {
    padding: 0.5rem 0;
    background: rgba(255, 255, 255, 0.98);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.logo-icon {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.navbar-brand:hover .logo-icon {
    transform: rotate(15deg);
}

.brand-content {
    display: flex;
    flex-direction: column;
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1.2;
}

.brand-tagline {
    font-size: 0.75rem;
    color: var(--primary-color);
    font-weight: 500;
}

.nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.6rem 1rem;
    margin: 0 0.3rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    transition: 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    opacity: 0.8;
    transition: var(--transition);
}

.nav-text {
    position: relative;
}

.nav-text::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover .nav-text::after,
.nav-link.active .nav-text::after {
    width: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(46, 204, 113, 0.05);
}

.navbar-actions {
    margin-right: 1rem;
}

.btn-contact {
    background: var(--gradient-primary);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
    transition: all 0.3s ease;
    border: none;
}

.btn-contact:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
    color: white;
}

/* Hero Section */
.services-hero {
    position: relative;
    min-height: 100vh;
    padding-top: 90px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e8f7f0 100%);
    overflow: hidden;
}

/* Advanced Background Elements */
.services-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 30%, rgba(46, 204, 113, 0.08) 0%, transparent 100px),
        radial-gradient(circle at 80% 70%, rgba(52, 152, 219, 0.07) 0%, transparent 150px);
    z-index: 0;
}

/* Animated grid background */
.services-hero::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: 
        linear-gradient(rgba(46, 204, 113, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(46, 204, 113, 0.03) 1px, transparent 1px);
    background-size: 40px 40px;
    z-index: 0;
    animation: gridMove 20s linear infinite;
}

/* Floating shapes */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    border-radius: 50%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    animation: float 10s infinite alternate ease-in-out;
}

.shape-1 {
    width: 150px;
    height: 150px;
    top: 15%;
    right: 10%;
    border-radius: 40% 60% 70% 30% / 40% 50% 50% 60%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(52, 152, 219, 0.05) 100%);
    animation-duration: 12s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    left: 5%;
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(46, 204, 113, 0.05) 100%);
    animation-duration: 15s;
    animation-delay: 1s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 15%;
    right: 20%;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(46, 204, 113, 0.05) 100%);
    animation-duration: 10s;
    animation-delay: 2s;
}

/* Content styling */
.services-hero .row {
    position: relative;
    z-index: 2;
}

.services-hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(to right, var(--primary-color), #34c759);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.services-hero h1::before {
    content: '';
    position: absolute;
    width: 100px;
    height: 5px;
    bottom: -15px;
    left: 0;
    background: var(--gradient-primary);
    border-radius: 10px;
}

.services-hero p.lead {
    font-size: 1.25rem;
    color: var(--text-color);
    max-width: 90%;
    line-height: 1.8;
    margin-bottom: 2rem;
}

/* Feature items */
.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 0.8rem 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.9);
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-left: 0.8rem;
}

.feature-item span {
    font-weight: 600;
    color: var(--dark-color);
}

/* Hero image styling */
.hero-image {
    position: relative;
    margin-bottom: 1rem;
}

.hero-image img {
    width: 100%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    transition: all 0.5s ease;
}

.hero-image:hover img {
    transform: translateY(-5px);
}

/* 3D decorative elements */
.hero-image::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px dashed rgba(46, 204, 113, 0.3);
    border-radius: 20px;
    top: 15px;
    left: 15px;
    z-index: -1;
    animation: pulse 3s infinite alternate;
}

.hero-image::after {
    content: '';
    position: absolute;
    width: 150px;
    height: 150px;
    background: rgba(46, 204, 113, 0.05);
    border-radius: 50%;
    bottom: -40px;
    right: -40px;
    z-index: -1;
    filter: blur(30px);
}

/* Service badge */
.service-badge {
    position: absolute;
    bottom: 30px;
    left: -15px;
    background: var(--gradient-primary);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 10px 25px rgba(46, 204, 113, 0.25);
    z-index: 3;
    transform: rotate(-5deg);
    transition: all 0.3s ease;
}

.service-badge::before {
    content: '';
    position: absolute;
    width: 15px;
    height: 15px;
    background: var(--secondary-color);
    bottom: -5px;
    left: 10px;
    transform: rotate(45deg);
    z-index: -1;
}

.hero-image:hover .service-badge {
    transform: rotate(0deg) translateY(-5px);
}

/* Tech lines decoration */
.tech-lines {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background-image: 
        linear-gradient(90deg, rgba(46, 204, 113, 0.1) 1px, transparent 1px),
        linear-gradient(rgba(46, 204, 113, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: center;
    border-radius: 0 0 0 100%;
    opacity: 0.5;
    z-index: 1;
}

/* Floating data elements */
.data-point {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 0.6rem 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.5);
    animation: float 5s infinite alternate ease-in-out;
}

.data-point i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.data-point-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0.5s;
}

.data-point-2 {
    bottom: 15%;
    left: 15%;
    animation-delay: 1s;
}

/* Keyframe animations */
@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(2deg);
    }
    100% {
        transform: translateY(0px) rotate(-2deg);
    }
}

@keyframes pulse {
    0% {
        opacity: 0.3;
        transform: scale(0.98);
    }
    100% {
        opacity: 0.6;
        transform: scale(1.01);
    }
}

@keyframes gridMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 40px 40px;
    }
}

/* Main Services Section - 2025 Futuristic Design */
.main-services {
    padding: 100px 0;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.main-services::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    right: -150px;
    top: -150px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.05) 0%, transparent 70%);
    border-radius: 50%;
}

.main-services::after {
    content: '';
    position: absolute;
    width: 400px;
    height: 400px;
    left: -200px;
    bottom: -200px;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.05) 0%, transparent 70%);
    border-radius: 50%;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    background: linear-gradient(to right, var(--primary-color), #34c759);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-color);
    max-width: 600px;
    margin: 0 auto 2rem;
}

/* Modern Service Cards with 3D Effects */
.service-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 1;
    overflow: hidden;
    height: 100%;
    border: 1px solid rgba(46, 204, 113, 0.05);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.03) 0%, rgba(52, 152, 219, 0.03) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.1);
    border-color: rgba(46, 204, 113, 0.2);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 25px rgba(46, 204, 113, 0.2);
    position: relative;
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    transform: rotateY(0deg);
}

.service-card:hover .service-icon {
    transform: rotateY(180deg);
}

.service-icon::before,
.service-icon::after {
    backface-visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
}

.service-icon::before {
    content: '';
    z-index: 2;
    transform: rotateY(0deg);
}

.service-icon::after {
    content: '→';
    background: var(--gradient-primary);
    transform: rotateY(180deg);
    z-index: 1;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
    position: relative;
    padding-bottom: 0.5rem;
}

.service-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.service-card:hover h3::after {
    width: 80px;
}

.service-card p {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.service-features {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.service-features li {
    margin-bottom: 0.8rem;
    position: relative;
    padding-right: 1.8rem;
    font-size: 0.95rem;
    color: var(--text-color);
}

.service-features li i {
    color: var(--primary-color);
    position: absolute;
    right: 0;
    top: 0.25rem;
    transition: transform 0.3s ease;
}

.service-card:hover .service-features li i {
    transform: scale(1.2);
}

.btn-service {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 30px;
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-service::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    z-index: -1;
}

.btn-service:hover {
    color: white;
}

.btn-service:hover::before {
    width: 100%;
}

/* Hover Glow Effect */
.service-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    transform: translate3d(-50%, -50%, 0);
    pointer-events: none;
}

.service-card:hover::after {
    opacity: 0.8;
}

/* Floating indicator */
.service-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--primary-color);
    opacity: 0;
    transition: all 0.3s ease;
}

.service-card:hover .service-indicator {
    opacity: 1;
    box-shadow: 0 0 0 5px rgba(46, 204, 113, 0.2);
    animation: pulse 1.5s infinite;
}

/* Service Particles - Floating elements */
.service-particle {
    position: absolute;
    background: rgba(46, 204, 113, 0.2);
    border-radius: 50%;
    z-index: 1;
    pointer-events: none;
    opacity: 0.5;
    animation: floatParticle 10s infinite linear;
}

@keyframes floatParticle {
    0% {
        transform: translateY(0) translateX(0) rotate(0);
    }
    33% {
        transform: translateY(-30px) translateX(20px) rotate(45deg);
    }
    66% {
        transform: translateY(20px) translateX(-15px) rotate(15deg);
    }
    100% {
        transform: translateY(0) translateX(0) rotate(0);
    }
}

/* Process Section - Futuristic Timeline */
.process-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #f0f6ff 100%);
    position: relative;
    overflow: hidden;
}

.process-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 10% 90%, rgba(46, 204, 113, 0.05) 0%, transparent 250px),
        radial-gradient(circle at 90% 10%, rgba(52, 152, 219, 0.05) 0%, transparent 250px);
    z-index: 0;
}

.process-timeline {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding: 50px 0;
    z-index: 1;
}

.process-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, 
        rgba(46, 204, 113, 0.1),
        rgba(46, 204, 113, 0.3),
        rgba(52, 152, 219, 0.3),
        rgba(52, 152, 219, 0.1)
    );
    transform: translateX(-50%);
    border-radius: 4px;
}

.process-item {
    position: relative;
    margin-bottom: 50px;
    width: 50%;
    padding: 0 40px;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.process-item.active {
    opacity: 1;
    transform: translateY(0);
}

.process-item:nth-child(odd) {
    left: 0;
    text-align: right;
}

.process-item:nth-child(even) {
    left: 50%;
    text-align: left;
}

.process-number {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
    z-index: 2;
}

.process-item:nth-child(odd) .process-number {
    right: -30px;
}

.process-item:nth-child(even) .process-number {
    left: -30px;
}

.process-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all 0.3s ease;
}

.process-item:hover .process-content {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.process-content::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: white;
    transform: rotate(45deg);
    z-index: -1;
}

.process-item:nth-child(odd) .process-content::before {
    right: -10px;
    top: 30px;
}

.process-item:nth-child(even) .process-content::before {
    left: -10px;
    top: 30px;
}

.process-content h3 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-weight: 700;
}

.process-content p {
    margin-bottom: 0;
    color: var(--text-color);
}

/* Process item connection lines */
.process-item::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background: rgba(46, 204, 113, 0.3);
    top: 30px;
}

.process-item:nth-child(odd)::after {
    right: 0;
}

.process-item:nth-child(even)::after {
    left: 0;
}

/* CTA Section - Futuristic Design */
.cta-section {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    color: white;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
        linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
}

.cta-section::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 60%);
    animation: rotateCTA 20s infinite linear;
}

@keyframes rotateCTA {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.cta-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cta-text {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-cta-white {
    background: white;
    color: var(--primary-color);
    border-radius: 30px;
    padding: 0.8rem 2rem;
    font-weight: 600;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
}

.btn-cta-white:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.btn-cta-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 30px;
    padding: 0.8rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-cta-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
}

/* Responsive styles for process timeline */
@media (max-width: 768px) {
    .process-timeline::before {
        left: 30px;
    }
    
    .process-item {
        width: 100%;
        left: 0 !important;
        text-align: left !important;
        padding-left: 80px;
        padding-right: 0;
    }
    
    .process-item:nth-child(odd) .process-number,
    .process-item:nth-child(even) .process-number {
        left: 0;
        right: auto;
    }
    
    .process-item::after,
    .process-item:nth-child(odd)::after,
    .process-item:nth-child(even)::after {
        left: 30px;
        width: 50px;
    }
    
    .process-item:nth-child(odd) .process-content::before,
    .process-item:nth-child(even) .process-content::before {
        left: -10px;
        right: auto;
    }
}

/* Modern Footer 2025 Design */
.footer-modern {
    position: relative;
    background: var(--dark-color);
    color: white;
}

.footer-top {
    padding: 80px 0 50px;
    position: relative;
    z-index: 1;
}

.footer-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(0deg, var(--dark-color) 0%, rgba(46, 61, 80, 0.9) 100%);
    z-index: -1;
}

.footer-brand .logo-icon {
    background: white;
    color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.footer-brand .brand-text {
    color: white;
}

.footer-brand .brand-tagline {
    color: #bdc3c7;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-icon {
    width: 38px;
    height: 38px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.social-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-5px);
}

.footer-widget {
    margin-bottom: 1.5rem;
}

.widget-title {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.8rem;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 2px;
    background: var(--primary-color);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.footer-links a i {
    transition: transform 0.3s ease;
    margin-left: 0.5rem;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(-5px);
}

.footer-links a:hover i {
    transform: translateX(-5px);
}

.footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-contact li {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1rem;
    flex-shrink: 0;
}

.contact-text span {
    display: block;
    font-size: 0.8rem;
    color: #bdc3c7;
    margin-bottom: 0.2rem;
}

.contact-text p {
    margin: 0;
    color: white;
}

.footer-bottom {
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    background: rgba(0, 0, 0, 0.2);
}

.copyright {
    margin: 0;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    justify-content: flex-end;
    gap: 1.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-bottom-links a {
    color: #bdc3c7;
    font-size: 0.9rem;
    text-decoration: none;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .process-timeline::before {
        left: 30px;
    }
    
    .process-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .process-number {
        margin-bottom: 1rem;
    }
    
    .process-content {
        margin-right: 60px;
    }

    .navbar-actions {
        margin-top: 1rem;
        margin-right: 0;
    }

    .btn-contact {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .footer-bottom-links {
        justify-content: center;
        margin-top: 1rem;
    }

    .copyright {
        text-align: center;
    }
}

@media (max-width: 767.98px) {
    .services-hero {
        padding: 100px 0 60px;
    }
    
    .hero-features {
        justify-content: center;
    }
    
    .service-badge {
        right: 0;
    }
    
    .section-title {
        font-size: 2rem;
    }

    .nav-link {
        padding: 0.8rem 1rem;
        margin: 0.2rem 0;
    }

    .footer-widget {
        text-align: center;
    }

    .widget-title::after {
        right: 50%;
        transform: translateX(50%);
    }

    .footer-links a {
        justify-content: center;
    }

    .footer-contact li {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .contact-icon {
        margin-bottom: 0.5rem;
    }
}

/* Custom Animation for Navbar on Scroll */
@keyframes navbarScroll {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.navbar-modern.scrolled {
    animation: navbarScroll 0.5s forwards;
}

/* Timeline Container */
.timeline-container {
    position: relative;
    padding: 30px 0;
}

/* Center Line */
.center-line {
    position: absolute;
    width: 4px;
    background: linear-gradient(to bottom, 
        rgba(46, 204, 113, 0.1),
        rgba(46, 204, 113, 0.5),
        rgba(52, 152, 219, 0.5),
        rgba(52, 152, 219, 0.1)
    );
    top: 0;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.2);
}

/* Timeline Items */
.timeline-item {
    position: relative;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.timeline-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

/* Right and Left Items Specific Styling */
.right-item {
    padding-right: 30px;
    text-align: right;
}

.left-item {
    padding-left: 30px;
    text-align: left;
}

/* Connector Lines */
.right-item:before {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: var(--primary-color);
    top: 50%;
    left: -20px;
}

.left-item:before {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: var(--primary-color);
    top: 50%;
    right: -20px;
}

/* Timeline Badge */
.timeline-badge {
    position: absolute;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.3rem;
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
    z-index: 2;
}

.right-item .timeline-badge {
    left: -25px;
    top: calc(50% - 25px);
}

.left-item .timeline-badge {
    right: -25px;
    top: calc(50% - 25px);
}

/* Content Styling */
.timeline-content {
    padding: 20px;
}

.timeline-content h3 {
    margin-bottom: 10px;
    font-weight: 700;
    color: var(--dark-color);
}

.timeline-content p {
    margin-bottom: 0;
    color: var(--text-color);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .center-line {
        left: 30px;
    }
    
    .right-item, .left-item {
        margin-left: 60px;
        text-align: left;
        padding-left: 30px;
        padding-right: 15px;
    }
    
    .right-item .timeline-badge, .left-item .timeline-badge {
        left: -25px;
        right: auto;
    }
    
    .right-item:before, .left-item:before {
        left: -20px;
        right: auto;
    }
} 